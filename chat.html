<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent <PERSON><PERSON><PERSON> Chat - HustlePlug</title>
  <link rel="stylesheet" href="styles/styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
  <div class="chat-container">
    <!-- Chat Header -->
    <div class="chat-header">
      <div class="header-left">
        <div class="logo">
          <div class="logo-icon">
            <svg width="32" height="32" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="url(#rocketGradient)"/>
              <defs>
                <linearGradient id="rocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#FF5C5C"/>
                  <stop offset="50%" style="stop-color:#FFD84D"/>
                  <stop offset="100%" style="stop-color:#5BA9F9"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <div class="logo-text">
            <h1>Agent Hustle</h1>
            <span class="pro-badge">PRO</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <button id="minimizeChat" class="window-control-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 12h12" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
        <button id="closeChatWindow" class="window-control-btn close-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Chat Content -->
    <div class="chat-content">
      <!-- Chat Messages -->
      <div id="chatMessages" class="chat-messages">
        <!-- Welcome message will be added by JavaScript -->
      </div>
      
      <!-- Hidden elements for JavaScript compatibility -->
      <div id="conversation-list" style="display: none;"></div>
      <button id="new-chat-btn" style="display: none;">New Chat</button>
      
      <!-- Chat Input -->
      <div class="chat-input-container">
        <div class="chat-input-wrapper">
          <textarea id="chatInput" placeholder="Type your message here..."></textarea>
          <button id="sendMessage" class="send-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        <div class="chat-input-footer">
          <div id="chatStatus" class="chat-status">
            <span class="status-indicator"></span>
            <span class="status-text">Ready</span>
          </div>
          <div class="character-count">0/2000</div>
        </div>
      </div>
    </div>
    
    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="loading-indicator" style="display: none;">
      <div class="loading-spinner"></div>
      <span>Processing...</span>
    </div>
  </div>

  <script src="chat.js"></script>
</body>
</html>
