<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent <PERSON><PERSON><PERSON> - <PERSON>lePlug</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background: #141021;
    }
    .typing-indicator span {
      animation: blink 1.4s infinite both;
    }
    .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
    .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }
    @keyframes blink {
      0% { opacity: 0.1; }
      20% { opacity: 1; }
      100% { opacity: 0.1; }
    }
    .hustleplug-gradient {
      background: linear-gradient(135deg, #5BA9F9 0%, #3F2B96 100%);
    }
    .hustleplug-bg {
      background: #141021;
    }
    .hustleplug-secondary {
      background: #1D1A2A;
    }
    .hustleplug-border {
      border-color: #2C2738;
    }
    .status-indicator.ready {
      background-color: #4CAF50;
    }
    .status-indicator.typing {
      background-color: #FFD84D;
    }
    .status-indicator.disconnected {
      background-color: #FF5C5C;
    }
    .status-indicator.connected {
      background-color: #4CAF50;
    }
  </style>
</head>
<body class="hustleplug-bg h-screen p-8 flex items-center justify-center">
  <div class="w-full max-w-6xl h-[700px] rounded-lg hustleplug-bg shadow-2xl overflow-hidden border hustleplug-border relative">
    <!-- Window Controls Bar -->
    <div class="h-6 hustleplug-secondary flex items-center px-3">
      <div class="flex items-center space-x-1.5">
        <div class="w-3 h-3 rounded-full bg-red-500"></div>
        <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
        <div class="w-3 h-3 rounded-full bg-green-500"></div>
      </div>
    </div>

    <!-- App Content -->
    <div class="flex h-[calc(100%-1.5rem)]">
      <!-- Sidebar -->
      <div class="w-64 hustleplug-secondary flex flex-col border-r hustleplug-border">
        <!-- New Chat Button -->
        <div class="p-4">
          <button id="new-chat-btn" class="w-full flex items-center justify-between rounded-md border hustleplug-border px-3 py-2 text-sm font-medium hover:bg-[#2C2738] text-white transition-all duration-200">
            <span class="flex items-center">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg"><path d="M12 4.5v15m7.5-7.5h-15"></path></svg>
              New chat
            </span>
          </button>
        </div>

        <!-- Recent Conversations -->
        <div class="flex-1 overflow-y-auto">
          <div class="px-3 py-2">
            <h3 class="text-xs text-[#B2AFC5] font-medium mb-2">Today</h3>
            <div id="conversation-list" class="space-y-1">
              <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-[#2C2738] flex items-center justify-between group text-white transition-all duration-200">
                <div class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                  Explaining quantum computing
                </div>
              </button>
              <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-[#2C2738] flex items-center justify-between group text-[#B2AFC5] transition-all duration-200">
                <div class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                  Creative writing prompts
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- User Profile -->
        <div class="border-t hustleplug-border pt-2 pb-4">
          <div class="px-3 py-2">
            <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-[#2C2738] flex items-center text-[#B2AFC5] transition-all duration-200">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg"><path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Zm0 0a9 9 0 0 0 5.636-1.968m-11.272 0A9 9 0 0 0 12 21Z"></path><circle cx="12" cy="9" r="3"></circle></svg>
              Upgrade to Pro
            </button>
            <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-[#2C2738] flex items-center justify-between text-white transition-all duration-200">
              <div class="flex items-center">
                <div class="h-7 w-7 rounded-full hustleplug-gradient flex items-center justify-center mr-2">
                  <span class="text-xs font-medium">HP</span>
                </div>
                <span>HustlePlug User</span>
              </div>
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg"><path d="M12 12h.01M12 6h.01M12 18h.01"></path></svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Main Chat Area -->
      <div class="flex-1 flex flex-col overflow-hidden relative">
        <!-- Chat Header -->
        <div class="border-b hustleplug-border p-3 flex items-center justify-between hustleplug-secondary">
          <div class="flex items-center">
            <span class="font-medium text-white">Agent Hustle</span>
            <span class="ml-2 px-2 py-1 rounded-md hustleplug-gradient text-xs text-white">PRO</span>
          </div>
          <div class="flex items-center space-x-2">
            <button id="minimizeChat" class="p-1.5 rounded-md hover:bg-[#2C2738] text-[#B2AFC5] transition-all duration-200">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"><path d="M12 5v14m-7-7h14"></path></svg>
            </button>
            <button id="closeChatWindow" class="p-1.5 rounded-md hover:bg-[#2C2738] text-[#B2AFC5] transition-all duration-200">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"><path d="M18 6L6 18M6 6l12 12"></path></svg>
            </button>
          </div>
        </div>

        <!-- Chat Messages -->
        <div id="chatMessages" class="flex-1 overflow-y-auto p-4 space-y-6 hustleplug-bg">
          <!-- System Welcome Message -->
          <div class="flex items-start">
            <div class="h-8 w-8 rounded-full hustleplug-gradient flex items-center justify-center flex-shrink-0 mr-3">
              <svg fill="currentColor" viewBox="0 0 24 24" class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div class="flex-1">
              <div class="text-sm text-[#F9F9F9]">
                <p class="mb-2"><strong>Agent Hustle</strong></p>
                <p>Hello! I'm Agent Hustle, your AI-powered productivity assistant from HustlePlug. I'm here to help you analyze content, boost your productivity, and achieve your goals. How can I assist you today?</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Input Area -->
        <div class="p-4 border-t hustleplug-border hustleplug-secondary">
          <div class="relative rounded-lg border hustleplug-border bg-[#2C2738] shadow-sm">
            <textarea id="chatInput" class="w-full p-3 pr-12 text-sm bg-transparent focus:outline-none resize-none text-white placeholder-[#B2AFC5]" rows="1" placeholder="Message Agent Hustle..."></textarea>
            <button id="sendMessage" class="absolute right-2 bottom-2 p-1 rounded-md text-[#B2AFC5] hover:bg-[#3D3A4A] transition-all duration-200">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 12L3.269 3.126A59.768 59.768 0 0721.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5"></path>
              </svg>
            </button>
          </div>
          <div class="mt-2 flex justify-between items-center text-xs text-[#B2AFC5]">
            <div id="chatStatus" class="flex items-center">
              <span class="status-indicator w-2 h-2 rounded-full bg-green-500 mr-2"></span>
              <span class="status-text">Ready</span>
            </div>
            <div class="character-count">0/2000</div>
          </div>
          <div class="text-xs text-center text-[#B2AFC5] mt-1">
            Agent Hustle can make mistakes. Consider checking important information.
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-[#1D1A2A] border border-[#2C2738] rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#5BA9F9]"></div>
        <span class="text-white">Processing...</span>
      </div>
    </div>
  </div>

  <script src="chat.js"></script>
</body>
</html>
